import { Header } from '@/app/[locale]/(authenticated)/components/header';
import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import {
  <PERSON><PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { title } from 'radash';
import type { ReactElement } from 'react';
import { BarcodeScannerTab } from '../../return-requests/[id]/barcode-scanner-tab';
import { ReturnRequestImageDialog } from '../../return-requests/[id]/page.client';
import { ReturnRequestStatusForm } from '../../return-requests/[id]/return-request-status-form';
import { getReturnRequest } from '../../return-requests/actions';

function getStatusColor(status: string): string {
  switch (status) {
    case 'completed':
      return 'text-green-500';
    case 'approved':
      return 'text-blue-500';
    case 'rejected':
      return 'text-red-500';
    default:
      return 'text-yellow-500';
  }
}

type PageProps = {
  readonly params: Promise<{
    id: string;
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const { returnRequest } = await getReturnRequest(id);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.exchange_requests.title}`,
    description: `${dictionary.admin.exchange_requests.detail.exchange_information} ${returnRequest.returnNumber}`,
  };
}

export default async function ExchangeRequestDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const { returnRequest, isBlacklisted, returnCount } =
    await getReturnRequest(id);

  return (
    <>
      <Header
        pages={[dictionary.admin.exchange_requests.title]}
        page={returnRequest.returnNumber}
      />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            {dictionary.admin.exchange_requests.title}{' '}
            {returnRequest.returnNumber}
          </h2>
          <ReturnRequestStatusForm
            id={returnRequest.id}
            currentStatus={returnRequest.status}
            processed={returnRequest.processed}
          />
        </div>
        {isBlacklisted && (
          <Alert variant="destructive">
            <AlertTitle>
              {dictionary.admin.return_requests.detail.blacklisted_title}
            </AlertTitle>
            <AlertDescription>
              <span>
                {dictionary.admin.return_requests.detail.blacklisted_description.replace(
                  '{count}',
                  returnCount.toString()
                )}
              </span>
            </AlertDescription>
          </Alert>
        )}
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="summary">
                {dictionary.admin.exchange_requests.detail.summary}
              </TabsTrigger>
              <TabsTrigger value="scanner">
                {dictionary.admin.exchange_requests.detail.barcode_scanner}
              </TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="summary" className="mt-6 space-y-4">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>
                    {
                      dictionary.admin.exchange_requests.detail
                        .exchange_information
                    }
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.exchange_requests.detail.order}
                    </span>
                    <span>{returnRequest.orderName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.exchange_requests.detail.customer_email}
                    </span>
                    <span className={isBlacklisted ? 'text-red-500' : ''}>
                      {returnRequest.email}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.return_requests.return_reason}
                    </span>
                    <span className="capitalize">
                      {returnRequest.returnReason.replace(/_/g, ' ')}
                    </span>
                  </div>
                  {returnRequest.exchangeType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {
                          dictionary.admin.exchange_requests.detail
                            .exchange_type
                        }
                      </span>
                      <span>{returnRequest.exchangeType}</span>
                    </div>
                  )}
                  {returnRequest.defectType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.detail.defect_type}
                      </span>
                      <span>{returnRequest.defectType}</span>
                    </div>
                  )}
                  {returnRequest.defectDetails && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.detail.defect_details}
                      </span>
                      <span>{returnRequest.defectDetails}</span>
                    </div>
                  )}
                  {returnRequest.returnLabelOption && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {
                          dictionary.admin.return_requests.detail
                            .return_label_option
                        }
                      </span>
                      <span className="capitalize">
                        {returnRequest.returnLabelOption}
                      </span>
                    </div>
                  )}
                  {returnRequest.refundFee !== null && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.detail.refund_fee}
                      </span>
                      <span>${returnRequest.refundFee.toFixed(2)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {
                        dictionary.admin.exchange_requests.detail
                          .status_information
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.status}
                      </span>
                      <span
                        className={cn(
                          getStatusColor(returnRequest.status),
                          'capitalize'
                        )}
                      >
                        {returnRequest.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.processed}
                      </span>
                      <span
                        className={
                          returnRequest.processed === 'completed'
                            ? 'text-green-500'
                            : 'text-red-500'
                        }
                      >
                        {title(returnRequest.processed)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.return_requests.created_at}
                      </span>
                      <span>{formatDateTime(returnRequest.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        {dictionary.admin.exchange_requests.detail.last_updated}
                      </span>
                      <span>{formatDateTime(returnRequest.updatedAt)}</span>
                    </div>
                    {returnRequest.adminNotes && (
                      <div className="mt-4">
                        <span className="text-muted-foreground">
                          {dictionary.admin.return_requests.admin_notes}
                        </span>
                        <p className="mt-1 whitespace-pre-wrap">
                          {returnRequest.adminNotes}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                {returnRequest.trackingNumber && (
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {
                          dictionary.admin.exchange_requests.detail
                            .tracking_information
                        }
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          {
                            dictionary.admin.exchange_requests.detail
                              .tracking_number
                          }
                        </span>
                        <span>{returnRequest.trackingNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          {
                            dictionary.admin.exchange_requests.detail
                              .tracking_company
                          }
                        </span>
                        <span>{returnRequest.trackingCompanyName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="min-w-[140px] text-muted-foreground">
                          {
                            dictionary.admin.exchange_requests.detail
                              .tracking_url
                          }
                        </span>
                        <a
                          href={returnRequest.trackingUrl ?? ''}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="overflow-hidden text-ellipsis whitespace-nowrap text-right text-blue-500"
                        >
                          {returnRequest.trackingUrl}
                        </a>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>
                  {dictionary.admin.exchange_requests.detail.exchange_items}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        {dictionary.admin.exchange_requests.detail.item}
                      </TableHead>
                      <TableHead>
                        {dictionary.admin.exchange_requests.detail.variant}
                      </TableHead>
                      <TableHead>
                        {dictionary.admin.exchange_requests.detail.quantity}
                      </TableHead>
                      <TableHead>
                        {dictionary.admin.exchange_requests.detail.price}
                      </TableHead>
                      <TableHead>
                        {dictionary.admin.return_requests.return_reason}
                      </TableHead>
                      <TableHead>
                        {
                          dictionary.admin.exchange_requests.detail
                            .exchange_variant
                        }
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {returnRequest.returnItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.title}</TableCell>
                        <TableCell>{item.variantTitle || '-'}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>
                          {item.price.toFixed(2)} {item.currency}
                        </TableCell>
                        <TableCell>{title(item.returnReason) || '-'}</TableCell>
                        <TableCell>{item.exchangeVariant || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  {dictionary.admin.return_requests.defect_photos}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {returnRequest.defectPhotos.length === 0 ? (
                  <p className="text-muted-foreground">
                    {dictionary.admin.return_requests.detail.no_defect_photos}
                  </p>
                ) : (
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                    {returnRequest.defectPhotos.map((photo) => (
                      <ReturnRequestImageDialog
                        key={photo.id}
                        photo={photo.url}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="scanner" className="mt-6">
            <BarcodeScannerTab
              returnRequest={returnRequest}
              dictionary={dictionary}
            />
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
